import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import type { User } from "@supabase/supabase-js";
import { useNavigate, useLocation } from "react-router";
import { useCampaignStore } from "@/stores/useCampaignStore";

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    let mounted = true;

    supabase.auth.getSession().then(({ data }) => {
      if (!mounted) return;
      setUser(data.session?.user ?? null);
      setLoading(false);

      if (data.session?.user && location.pathname === "/login") {
        navigate("/", { replace: true });
      } else if (!data.session?.user && location.pathname !== "/login") {
        navigate("/login", { replace: true });
      }
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (!mounted) return;
      setUser(session?.user ?? null);
      setLoading(false);

      if (location.pathname === "/login") {
        navigate("/", { replace: true });
      } else if (event === "SIGNED_OUT" || (!session?.user && event !== "INITIAL_SESSION")) {
        navigate("/login", { replace: true });
      }
    });
    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [navigate, location.pathname]);

  const logout = async () => {
    await supabase.auth.signOut();

    const campaignStore = useCampaignStore.getState();
    campaignStore.setCampaign(null);

    setUser(null);
    navigate("/login");
  };

  return { user, loading, logout };
}
