import { Form, FormControl, FormItem, FormField } from "@/components/ui/Form";
import { Input } from "@/components/ui/Input";
import { SidebarGroup, SidebarGroupLabel } from "@/components/ui/Sidebar";
import { useFolderStore } from "@/stores/useFolderStore";
import { zodResolver } from "@hookform/resolvers/zod";
import { CopyMinus, Minus, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import z from "zod";
import { WorldbuildingFolders } from "./WorldbuildingFolders";
import { Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/Tooltip";

const createFolderSchema = z.object({
  name: z.string(),
});

type CreateFolderFormData = z.infer<typeof createFolderSchema>;

export const WorldbuildingMenu = () => {
  const form = useForm<CreateFolderFormData>({
    resolver: zodResolver(createFolderSchema),
    defaultValues: {
      name: "",
    },
  });

  const folderStore = useFolderStore();

  const [showCreateFolderInput, setShowCreateFolderInput] = useState(false);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (showCreateFolderInput) {
      const currentNameInput = document.getElementById("create-folder-name");
      currentNameInput?.focus();
    }
  }, [showCreateFolderInput]);

  const folderItems = folderStore.folders.map((folder) => ({
    id: folder.id,
    name: folder.name,
    isActive: expandedFolders.has(folder.id),
    notes: folder.notes,
  }));

  const createFolder = async (data: CreateFolderFormData) => {
    if (!data.name.trim()) {
      setShowCreateFolderInput(false);
      return;
    }

    await folderStore.createFolder(data.name.trim());
    form.reset();
    setShowCreateFolderInput(false);
  };

  const collapseAll = () => {
    setExpandedFolders(new Set());
  };

  const toggleFolder = (folderId: string) => {
    setExpandedFolders((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>
        Worldbuilding
        <div className="ml-auto flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <CopyMinus className="cursor-pointer" size={16} onClick={() => collapseAll()} />
            </TooltipTrigger>
            <TooltipContent>Collapse all</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              {showCreateFolderInput ? (
                <Minus
                  className="cursor-pointer"
                  size={16}
                  onClick={() => setShowCreateFolderInput(false)}
                />
              ) : (
                <Plus
                  className="cursor-pointer"
                  size={16}
                  onClick={() => setShowCreateFolderInput(true)}
                />
              )}
            </TooltipTrigger>
            <TooltipContent>New folder</TooltipContent>
          </Tooltip>
        </div>
      </SidebarGroupLabel>
      {showCreateFolderInput && (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(createFolder)} className="mb-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      id="create-folder-name"
                      placeholder=""
                      className="ml-2 mr-2 w-auto"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
      )}

      <WorldbuildingFolders items={folderItems} onToggleFolder={toggleFolder} />
    </SidebarGroup>
  );
};
