import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/Collapsible";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
} from "@/components/ui/Sidebar";
import { ChevronRight, Plus, type LucideIcon } from "lucide-react";
import { FolderContextMenu } from "./FolderContextMenu";
import { CreateNoteDialog } from "../notes/CreateNoteDialog";
import { useState } from "react";
import { useFolderStore, type Note } from "@/stores/useFolderStore";
import { useNavigate } from "react-router";

export type FolderItem = {
  id: string;
  name: string;
  isActive: boolean;
  icon?: LucideIcon;
  notes?: Note[];
};

export const WorldbuildingFolders = ({
  items,
  onToggleFolder,
}: {
  items: FolderItem[];
  onToggleFolder: (folderId: string) => void;
}) => {
  const navigate = useNavigate();
  const folderStore = useFolderStore();
  const [createNoteDialogOpen, setCreateNoteDialogOpen] = useState(false);
  const [selectedFolderId, setSelectedFolderId] = useState<string | undefined>(undefined);

  const openCreateNoteDialog = (folderId: string) => {
    setSelectedFolderId(folderId);
    setCreateNoteDialogOpen(true);
  };

  const openNote = (noteId: string) => {
    folderStore.openNote(noteId);
    navigate("/notes");
  };

  return (
    <>
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible
            key={item.id}
            asChild
            open={item.isActive}
            onOpenChange={() => onToggleFolder(item.id)}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              <SidebarMenuButton tooltip={item.name} className="bg-sidebar-accent">
                <FolderContextMenu id={item.id}>
                  <div className="flex items-center w-full gap-2">
                    <CollapsibleTrigger asChild>
                      <span
                        className={`
                          flex items-center gap-2 flex-1 min-w-0
                          ${item.notes && item.notes.length > 0 ? "cursor-pointer" : ""}
                        `}
                      >
                        <ChevronRight
                          className={`
                            shrink-0 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 size-5
                            ${item.notes && item.notes.length > 0 ? "opacity-100" : "opacity-0"}
                          `}
                        />
                        {item.icon && <item.icon />}
                        <span className="ml-1 truncate" style={{ maxWidth: "100%" }}>
                          {item.name}
                        </span>
                      </span>
                    </CollapsibleTrigger>
                    <Plus
                      className="shrink-0 opacity-0 group-hover/collapsible:opacity-100 cursor-pointer size-5"
                      onClick={() => openCreateNoteDialog(item.id)}
                    />
                  </div>
                </FolderContextMenu>
              </SidebarMenuButton>
              {item.notes && item.notes.length > 0 && (
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.notes?.map((note) => (
                      <SidebarMenuSubItem
                        key={note.id}
                        className="text-sm cursor-pointer"
                        onClick={() => openNote(note.id)}
                      >
                        {note.title}
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              )}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>

      <CreateNoteDialog
        open={createNoteDialogOpen}
        onOpenChange={setCreateNoteDialogOpen}
        folderId={selectedFolderId}
      />
    </>
  );
};
