import { Tabs } from "@/components/ui/Tabs";
import { useFolderStore } from "@/stores/useFolderStore";
import { OctagonX } from "lucide-react";
import { useRef } from "react";

const truncateTitle = (title: string, maxLength: number = 30): string => {
  if (title.length <= maxLength) {
    return title;
  }
  return title.slice(0, maxLength - 3) + "...";
};

export const NoteTabs = () => {
  const folderStore = useFolderStore();
  const isClosingRef = useRef(false);

  const activeValue = folderStore.activeNote?.id || folderStore.recentNotes[0]?.id || "";

  const handleTabChange = (value: string) => {
    if (isClosingRef.current) {
      return;
    }
    folderStore.setActiveNote(value);
  };

  const handleCloseTab = (noteId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    isClosingRef.current = true;

    folderStore.closeNote(noteId);

    setTimeout(() => {
      isClosingRef.current = false;
    }, 0);
  };

  if (folderStore.recentNotes.length === 0) {
    return null;
  }

  return (
    <Tabs value={activeValue} onValueChange={handleTabChange}>
      <Tabs.List>
        {folderStore.recentNotes.map((note) => (
          <Tabs.Trigger
            key={note.id}
            value={note.id}
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground flex items-center justify-between"
            onAuxClick={(e) => {
              if (e.button === 1) {
                handleCloseTab(note.id, e);
              }
            }}
          >
            <span className="truncate flex-1 text-left" title={note.title || "Untitled"}>
              {truncateTitle(note.title || "Untitled")}
            </span>
            <OctagonX
              className="ml-2 cursor-pointer flex-shrink-0"
              size={16}
              onClick={(e) => handleCloseTab(note.id, e)}
            />
          </Tabs.Trigger>
        ))}
      </Tabs.List>
    </Tabs>
  );
};
