import { Button } from "@/components/ui/Button";
import { Dialog } from "@/components/ui/Dialog";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/Form";
import { Input } from "@/components/ui/Input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Textarea } from "@/components/ui/Textarea";
import { useFolderStore } from "@/stores/useFolderStore";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import z from "zod";

const createNoteSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().optional(),
  folderId: z.string().min(1, "Folder is required"),
});

export type CreateNoteFormData = z.infer<typeof createNoteSchema>;

export const CreateNoteDialog = ({
  open,
  onOpenChange,
  folderId = "",
  title = "",
  content = "",
  noteId,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  folderId?: string;
  title?: string;
  content?: string;
  noteId?: string;
}) => {
  const folderStore = useFolderStore();

  useEffect(() => {
    if (open) {
      folderStore.fetchFolders({});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const form = useForm<CreateNoteFormData>({
    resolver: zodResolver(createNoteSchema),
    values: {
      title,
      content,
      folderId,
    },
  });

  const onCreate = async (data: CreateNoteFormData) => {
    if (noteId) {
      await folderStore.updateNote(noteId, data);
    } else {
      await folderStore.createNote(data);
    }
    onOpenChange(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <Dialog.Content className="sm:max-w-[625px]">
        <Dialog.Header>
          <Dialog.Title>Create New Note</Dialog.Title>
        </Dialog.Header>
        <Dialog.Description className="text-sm text-muted-foreground mb-4">
          Create a new note to capture your ideas, stories, and worldbuilding details.
        </Dialog.Description>

        <hr />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onCreate)} className="space-y-4">
            <div className="grid grid-cols-12 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="col-span-12 md:col-span-8 w-full">
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Title of the note" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="folderId"
                render={({ field }) => (
                  <FormItem className="col-span-12 md:col-span-4 w-full min-w-0">
                    <FormLabel>Folder</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full min-w-0">
                          <SelectValue placeholder="Select a folder for the note" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {folderStore.folders.map((folder) => (
                          <SelectItem key={folder.id} value={folder.id}>
                            {folder.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Summary</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="A summary of the note's content"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Dialog.Footer>
              <Dialog.Close asChild>
                <Button variant="outline">Cancel</Button>
              </Dialog.Close>
              <Button type="submit">Create</Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
};
