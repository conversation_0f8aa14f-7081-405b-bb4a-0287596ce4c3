import { EditNote } from "@/components/features/notes/EditNote";
import { NoteTabs } from "@/components/features/notes/NoteTabs";
import { useFolderStore } from "@/stores/useFolderStore";
import { useEffect } from "react";
import { useNavigate } from "react-router";

export const Notes = () => {
  const folderStore = useFolderStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (folderStore.recentNotes.length === 0) {
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [folderStore.recentNotes]);

  return (
    <div className="w-full">
      <div className="max-w-[70vw] overflow-x-scroll h-12">
        <NoteTabs />
      </div>
      {folderStore.activeNote ? (
        <div className="ml-1 mt-4 w-full">
          <EditNote note={folderStore.activeNote} />
        </div>
      ) : null}
    </div>
  );
};
